<?php

namespace Tests\Feature;

use App\Traits\LarkTrait;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class LarkRetryMechanismTest extends TestCase
{
    use LarkTrait;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set test configuration
        Config::set('larksuite.retry.max_attempts', 3);
        Config::set('larksuite.retry.base_delay', 1);
        Config::set('larksuite.retry.max_delay', 10);
        Config::set('larksuite.retryable_codes.lark_api', [90235, 1310235]);
        Config::set('larksuite.retryable_messages', [
            'data not ready,retry later',
            'retry later'
        ]);
    }

    /** @test */
    public function it_identifies_retryable_lark_api_responses()
    {
        // Test retryable error code 90235
        $response1 = ['code' => 90235, 'msg' => 'data not ready,retry later'];
        $this->assertTrue($this->shouldRetryLarkResponse($response1));

        // Test retryable error code 1310235
        $response2 = ['code' => 1310235, 'msg' => 'Retry Later'];
        $this->assertTrue($this->shouldRetryLarkResponse($response2));

        // Test retryable message
        $response3 = ['code' => 0, 'msg' => 'data not ready,retry later'];
        $this->assertTrue($this->shouldRetryLarkResponse($response3));

        // Test non-retryable response
        $response4 = ['code' => 0, 'msg' => 'success'];
        $this->assertFalse($this->shouldRetryLarkResponse($response4));

        // Test null response
        $this->assertFalse($this->shouldRetryLarkResponse(null));
    }

    /** @test */
    public function it_calculates_exponential_backoff_delay()
    {
        Config::set('larksuite.retry.exponential_backoff', true);
        Config::set('larksuite.retry.jitter', false);
        Config::set('larksuite.retry.max_delay', 60);

        // Test exponential backoff without jitter
        $delay1 = $this->calculateBackoffDelay(1, 2); // 2 * 2^0 = 2
        $delay2 = $this->calculateBackoffDelay(2, 2); // 2 * 2^1 = 4
        $delay3 = $this->calculateBackoffDelay(3, 2); // 2 * 2^2 = 8

        $this->assertEquals(2, $delay1);
        $this->assertEquals(4, $delay2);
        $this->assertEquals(8, $delay3);
    }

    /** @test */
    public function it_calculates_linear_backoff_delay()
    {
        Config::set('larksuite.retry.exponential_backoff', false);
        Config::set('larksuite.retry.jitter', false);
        Config::set('larksuite.retry.max_delay', 60);

        // Test linear backoff without jitter
        $delay1 = $this->calculateBackoffDelay(1, 2); // 2 * 1 = 2
        $delay2 = $this->calculateBackoffDelay(2, 2); // 2 * 2 = 4
        $delay3 = $this->calculateBackoffDelay(3, 2); // 2 * 3 = 6

        $this->assertEquals(2, $delay1);
        $this->assertEquals(4, $delay2);
        $this->assertEquals(6, $delay3);
    }

    /** @test */
    public function it_respects_maximum_delay_cap()
    {
        Config::set('larksuite.retry.exponential_backoff', true);
        Config::set('larksuite.retry.jitter', false);
        Config::set('larksuite.retry.max_delay', 10);

        // Test that delay is capped at max_delay
        $delay = $this->calculateBackoffDelay(10, 5); // Would be 5 * 2^9 = 2560, but capped at 10
        $this->assertEquals(10, $delay);
    }

    /** @test */
    public function it_adds_jitter_when_enabled()
    {
        Config::set('larksuite.retry.exponential_backoff', true);
        Config::set('larksuite.retry.jitter', true);
        Config::set('larksuite.retry.max_delay', 60);

        // Test that jitter is added (delay should be >= base delay)
        $baseDelay = 4;
        $delay = $this->calculateBackoffDelay(2, 2); // Base would be 4, jitter adds 0-2
        
        $this->assertGreaterThanOrEqual($baseDelay, $delay);
        $this->assertLessThanOrEqual($baseDelay + 2, $delay); // Max jitter is 50% of base
    }

    /** @test */
    public function it_uses_config_defaults_for_retry_parameters()
    {
        // Test that methods use config defaults when parameters are null
        Config::set('larksuite.sheets.max_retries', 5);
        Config::set('larksuite.sheets.base_delay', 4);

        // This would normally require mocking HTTP calls, but we can test the config loading
        $this->assertEquals(5, config('larksuite.sheets.max_retries'));
        $this->assertEquals(4, config('larksuite.sheets.base_delay'));
    }

    /** @test */
    public function it_loads_retryable_codes_from_config()
    {
        Config::set('larksuite.retryable_codes.http', [429, 500, 502]);
        Config::set('larksuite.retryable_codes.lark_api', [90235, 1310235]);

        $response1 = ['code' => 429, 'msg' => 'rate limit'];
        $response2 = ['code' => 500, 'msg' => 'internal error'];
        $response3 = ['code' => 90235, 'msg' => 'data not ready'];

        $this->assertTrue($this->shouldRetryLarkResponse($response1));
        $this->assertTrue($this->shouldRetryLarkResponse($response2));
        $this->assertTrue($this->shouldRetryLarkResponse($response3));
    }

    /** @test */
    public function it_loads_retryable_messages_from_config()
    {
        Config::set('larksuite.retryable_messages', [
            'custom retry message',
            'temporary unavailable'
        ]);

        $response1 = ['code' => 0, 'msg' => 'Custom Retry Message'];
        $response2 = ['code' => 0, 'msg' => 'Service Temporary Unavailable'];
        $response3 = ['code' => 0, 'msg' => 'success'];

        $this->assertTrue($this->shouldRetryLarkResponse($response1));
        $this->assertTrue($this->shouldRetryLarkResponse($response2));
        $this->assertFalse($this->shouldRetryLarkResponse($response3));
    }
}

<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

return [
    'app_id' => env('LARK_APP_ID'),
    'app_secret' => env('LARK_APP_SECRET'),
    'chat_id' => env('LARK_CHAT_ID'),
    'stored_auth_code' => 'eFEj7HEFAIJ9Q0ICWcH4d36J1IX98GV2',
    'default_template_sheet_url' => env('LARK_DEFAULT_TEMPLATE_SHEET_URL'),

    /*
    |--------------------------------------------------------------------------
    | HTTP Client Configuration
    |--------------------------------------------------------------------------
    */
    'http' => [
        'timeout' => env('LARKSUITE_HTTP_TIMEOUT', 60),
        'connect_timeout' => env('LARKSUITE_CONNECT_TIMEOUT', 30),
        'read_timeout' => env('LARKSUITE_READ_TIMEOUT', 60),
    ],

    /*
    |--------------------------------------------------------------------------
    | Retry Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for handling API failures and retries
    |
    */
    'retry' => [
        'max_attempts' => env('LARKSUITE_MAX_RETRY_ATTEMPTS', 3),
        'base_delay' => env('LARKSUITE_BASE_DELAY', 2),
        'max_delay' => env('LARKSUITE_MAX_DELAY', 120),
        'exponential_backoff' => env('LARKSUITE_EXPONENTIAL_BACKOFF', true),
        'jitter' => env('LARKSUITE_RETRY_JITTER', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Retryable Error Codes
    |--------------------------------------------------------------------------
    |
    | HTTP status codes and Lark API error codes that should trigger retries
    |
    */
    'retryable_codes' => [
        'http' => [429, 500, 502, 503, 504],
        'lark_api' => [90235, 1310235],
    ],

    /*
    |--------------------------------------------------------------------------
    | Retryable Error Messages
    |--------------------------------------------------------------------------
    |
    | Error messages that indicate temporary issues and should trigger retries
    |
    */
    'retryable_messages' => [
        'data not ready,retry later',
        'retry later',
        'rate limit exceeded',
        'service temporarily unavailable',
        'internal server error',
        'bad gateway',
        'service unavailable',
        'gateway timeout',
    ],

    /*
    |--------------------------------------------------------------------------
    | Sheets API Specific Settings
    |--------------------------------------------------------------------------
    */
    'sheets' => [
        'max_retries' => env('LARKSUITE_SHEETS_MAX_RETRIES', 3),
        'base_delay' => env('LARKSUITE_SHEETS_BASE_DELAY', 3),
        'rate_limit_delay' => env('LARKSUITE_SHEETS_RATE_LIMIT_DELAY', 2),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'channel' => env('LARKSUITE_LOG_CHANNEL', 'lark_daily'),
        'log_requests' => env('LARKSUITE_LOG_REQUESTS', true),
        'log_responses' => env('LARKSUITE_LOG_RESPONSES', true),
        'log_retries' => env('LARKSUITE_LOG_RETRIES', true),
    ],
];

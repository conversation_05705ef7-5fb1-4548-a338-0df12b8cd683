# Lark API Retry Mechanism Implementation

## Overview

This document describes the enhanced retry mechanism implemented for the Lark API integration to handle timeout issues and temporary failures identified in the log analysis.

## Issues Identified

From the log analysis of `storage/logs/lark/lark_daily-2025-07-29.log`, the following issues were identified:

1. **Error Code 90235**: "data not ready,retry later" - Occurs when Lark API is processing data
2. **Error Code 1310235**: "Retry Later" - Temporary API unavailability
3. **Long Response Times**: Some requests taking 26+ seconds before timing out
4. **No Retry Logic**: Failed requests were not automatically retried

## Solution Implementation

### 1. Enhanced Timeout Configuration

**File**: `app/Traits/LarkTrait.php` - `sendRequest()` method

- **HTTP Timeout**: Increased from default to 60 seconds
- **Connect Timeout**: Set to 30 seconds
- **Read Timeout**: Set to 60 seconds

```php
$client = new Client([
    'timeout' => config('larksuite.http.timeout', 60),
    'connect_timeout' => config('larksuite.http.connect_timeout', 30),
    'read_timeout' => config('larksuite.http.read_timeout', 60),
]);
```

### 2. Comprehensive Retry Mechanism

**Features**:
- **Maximum Retry Attempts**: Configurable (default: 3)
- **Exponential Backoff**: Delays increase exponentially with each retry
- **Jitter**: Random delay component to prevent thundering herd
- **Smart Error Detection**: Identifies retryable vs non-retryable errors

**Retryable Conditions**:
- HTTP Status Codes: 429, 500, 502, 503, 504
- Lark API Error Codes: 90235, 1310235
- Error Messages: "data not ready,retry later", "retry later", etc.

### 3. Configuration System

**File**: `config/larksuite.php`

All retry parameters are configurable through environment variables:

```php
'retry' => [
    'max_attempts' => env('LARKSUITE_MAX_RETRY_ATTEMPTS', 3),
    'base_delay' => env('LARKSUITE_BASE_DELAY', 2),
    'max_delay' => env('LARKSUITE_MAX_DELAY', 120),
    'exponential_backoff' => env('LARKSUITE_EXPONENTIAL_BACKOFF', true),
    'jitter' => env('LARKSUITE_RETRY_JITTER', true),
],
```

### 4. Method-Specific Enhancements

All critical Lark API methods now support retry mechanisms:

- `appendValuesToSheet()` - For data insertion operations
- `updateCells()` - For cell updates
- `findCells()` - For cell searches
- `sendRequest()` - Base HTTP request method

## Configuration Guide

### Environment Variables

Add these to your `.env` file:

```env
# Basic retry settings
LARKSUITE_MAX_RETRY_ATTEMPTS=3
LARKSUITE_BASE_DELAY=2
LARKSUITE_MAX_DELAY=120

# HTTP timeouts
LARKSUITE_HTTP_TIMEOUT=60
LARKSUITE_CONNECT_TIMEOUT=30
LARKSUITE_READ_TIMEOUT=60

# Sheets-specific settings (higher values for slower operations)
LARKSUITE_SHEETS_MAX_RETRIES=3
LARKSUITE_SHEETS_BASE_DELAY=3
```

### Production Recommendations

For high-load production environments:

```env
LARKSUITE_HTTP_TIMEOUT=90
LARKSUITE_MAX_RETRY_ATTEMPTS=5
LARKSUITE_BASE_DELAY=3
LARKSUITE_MAX_DELAY=180
LARKSUITE_SHEETS_MAX_RETRIES=5
LARKSUITE_SHEETS_BASE_DELAY=5
```

## Usage Examples

### Basic Usage (uses config defaults)

```php
// Automatically retries with configured parameters
$result = $this->appendValuesToSheet($token, $spreadsheetKey, $range, $values);
```

### Custom Retry Parameters

```php
// Custom retry settings for specific operations
$result = $this->appendValuesToSheet(
    $token, 
    $spreadsheetKey, 
    $range, 
    $values,
    $maxRetries = 5,    // Override default
    $baseDelay = 4      // Override default
);
```

## Monitoring and Logging

### Log Entries

The enhanced system provides detailed logging:

```
[LarkTrait::sendRequest] Retrying request after delay
[LarkTrait::sendRequest] Lark API retry condition detected
[LarkTrait::sendRequest] Request succeeded after retry
[LarkTrait::sendRequest] All retry attempts exhausted
```

### Key Metrics to Monitor

1. **Retry Success Rate**: Percentage of operations that succeed after retry
2. **Average Retry Count**: How many retries are typically needed
3. **Timeout Frequency**: How often requests hit timeout limits
4. **Error Code Distribution**: Which error codes are most common

## Testing

Run the test suite to verify retry mechanism:

```bash
php artisan test tests/Feature/LarkRetryMechanismTest.php
```

## Troubleshooting

### Common Issues

1. **Still Getting Timeouts**: Increase `LARKSUITE_HTTP_TIMEOUT`
2. **Too Many Retries**: Reduce `LARKSUITE_MAX_RETRY_ATTEMPTS`
3. **Slow Performance**: Reduce `LARKSUITE_BASE_DELAY`

### Debug Mode

Enable detailed logging:

```env
LARKSUITE_LOG_REQUESTS=true
LARKSUITE_LOG_RESPONSES=true
LARKSUITE_LOG_RETRIES=true
```

## Performance Impact

- **Positive**: Reduced failure rates, better reliability
- **Consideration**: Increased latency for failed requests due to retries
- **Mitigation**: Configurable parameters allow tuning for specific needs

## Future Enhancements

1. **Circuit Breaker Pattern**: Temporarily disable retries if failure rate is too high
2. **Adaptive Delays**: Adjust delays based on API response patterns
3. **Metrics Collection**: Built-in metrics for monitoring retry effectiveness
4. **Rate Limiting**: Intelligent rate limiting based on API responses

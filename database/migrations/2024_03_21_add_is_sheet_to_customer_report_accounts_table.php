<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::table('customer_report_accounts', function (Blueprint $table) {
            $table->boolean('is_sheet')->default(false)->after('customer_report_id');
        });
    }

    public function down()
    {
        Schema::table('customer_report_accounts', function (Blueprint $table) {
            $table->dropColumn('is_sheet');
        });
    }
};

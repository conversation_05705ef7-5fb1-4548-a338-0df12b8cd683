<?php

namespace App\Http\Controllers\Extensions\TTCommentExporter;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class TTCommentExporterController extends Controller
{
    protected $mockResponses;

    public function __construct()
    {
        // Load mock responses from JSON file
        $this->mockResponses = json_decode(Storage::get('mock/paddle_responses.json'), true);
    }

    public function checkout(Request $request)
    {
        // Return mock checkout response
        return response()->json($this->mockResponses['checkout']);
    }

    public function updatePayment(Request $request)
    {
        // Return mock payment update response
        return response()->json($this->mockResponses['updatePayment']);
    }

    public function cancel(Request $request)
    {
        // Return mock cancellation response
        return response()->json($this->mockResponses['cancel']);
    }

    public function undoCancel(Request $request)
    {
        // Return mock cancellation reversal response
        return response()->json($this->mockResponses['undoCancel']);
    }

    public function customerPortal(Request $request)
    {
        // Mock customer portal URL
        return response()->json([
            'success' => true,
            'data' => [
                'portalUrl' => 'https://portal.toolmagic.app/customer/' . $request->input('customerId'),
                'expiresAt' => now()->addHour()->timestamp
            ],
            'timestamp' => now()->timestamp
        ]);
    }

    public function checkSubscription(Request $request)
    {
        // Get status from request or default to 'active'
        $status = $request->input('status', 'active');

        // Return appropriate mock response based on status
        return response()->json($this->mockResponses['checkSubscription'][$status]);
    }
}

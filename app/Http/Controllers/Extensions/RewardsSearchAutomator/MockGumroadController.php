<?php

namespace App\Http\Controllers\Extensions\RewardsSearchAutomator;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class MockGumroadController extends Controller
{
    protected $validLicenses = [
        'valid-key-123' => [
            'uses' => 2,
            'purchase' => [
                'product_id' => 'D-1vxIJJlbq1sZUhTpz70A==',
                'subscription_cancelled_at' => null,
                'subscription_ended_at' => null,
                'subscription_failed_at' => null,
                'disputed' => false,
                'email' => '<EMAIL>',
                'sale_id' => 'mock-sale-123',
                'created_at' => '2024-01-01'
            ]
        ],
        'expired-key-456' => [
            'uses' => 1,
            'purchase' => [
                'product_id' => 'D-1vxIJJlbq1sZUhTpz70A==',
                'subscription_cancelled_at' => '2024-02-01',
                'subscription_ended_at' => null,
                'subscription_failed_at' => null,
                'disputed' => false,
                'email' => '<EMAIL>',
                'sale_id' => 'mock-sale-456',
                'created_at' => '2024-01-01'
            ]
        ],
        'disputed-key-789' => [
            'uses' => 5,
            'purchase' => [
                'product_id' => 'D-1vxIJJlbq1sZUhTpz70A==',
                'subscription_cancelled_at' => null,
                'subscription_ended_at' => null,
                'subscription_failed_at' => null,
                'disputed' => true,
                'email' => '<EMAIL>',
                'sale_id' => 'mock-sale-789',
                'created_at' => '2024-01-01'
            ]
        ],
        'high-usage-key-999' => [
            'uses' => 10,
            'purchase' => [
                'product_id' => 'D-1vxIJJlbq1sZUhTpz70A==',
                'subscription_cancelled_at' => null,
                'subscription_ended_at' => null,
                'subscription_failed_at' => null,
                'disputed' => false,
                'email' => '<EMAIL>',
                'sale_id' => 'mock-sale-999',
                'created_at' => '2024-01-01'
            ]
        ],
        'wrong-product-key-111' => [
            'uses' => 1,
            'purchase' => [
                'product_id' => 'different-product-id',
                'subscription_cancelled_at' => null,
                'subscription_ended_at' => null,
                'subscription_failed_at' => null,
                'disputed' => false,
                'email' => '<EMAIL>',
                'sale_id' => 'mock-sale-111',
                'created_at' => '2024-01-01'
            ]
        ]
    ];

    public function verifyLicense(Request $request)
    {
        $licenseKey = $request->input('license_key');
        $productId = $request->input('product_id');
        $incrementUsesCount = $request->input('increment_uses_count', true);

        // Simulate network delay
        usleep(rand(100000, 500000)); // 100-500ms delay

        // Invalid license key
        if (!isset($this->validLicenses[$licenseKey])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid license key'
            ]);
        }

        $licenseInfo = $this->validLicenses[$licenseKey];
        $uses = $licenseInfo['uses'];

        // Increment uses if requested
        if ($incrementUsesCount !== false && $incrementUsesCount !== 'false') {
            $uses++;
            $this->validLicenses[$licenseKey]['uses'] = $uses;
        }

        return response()->json([
            'success' => true,
            'uses' => $uses,
            'purchase' => $licenseInfo['purchase']
        ]);
    }
}

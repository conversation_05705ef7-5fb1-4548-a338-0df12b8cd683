<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

namespace App\Http\Controllers;

use App\Http\Requests\CustomerReportRequest;
use App\Http\Resources\CustomerReportResource;
use App\Models\CustomerReport;

class CustomerReportController extends Controller
{
    public function index()
    {
        return CustomerReportResource::collection(CustomerReport::all());
    }

    public function store(CustomerReportRequest $request)
    {
        return new CustomerReportResource(CustomerReport::create($request->validated()));
    }

    public function show(CustomerReport $customerReport)
    {
        return new CustomerReportResource($customerReport);
    }

    public function update(CustomerReportRequest $request, CustomerReport $customerReport)
    {
        $customerReport->update($request->validated());

        return new CustomerReportResource($customerReport);
    }

    public function destroy(CustomerReport $customerReport)
    {
        $customerReport->delete();

        return response()->json();
    }
}

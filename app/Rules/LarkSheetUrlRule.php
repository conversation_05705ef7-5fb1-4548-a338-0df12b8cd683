<?php

namespace App\Rules;

use App\Traits\LarkSheetHelperTrait;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class LarkSheetUrlRule implements ValidationRule
{
    use LarkSheetHelperTrait;

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty($value)) {
            return; // Allow empty values, let required rule handle it
        }

        if (!$this->validateLarkSheetUrl($value)) {
            $fail('URL Lark Sheet không đúng định dạng. Ví dụ: https://togegroup.sg.larksuite.com/sheets/OD5VsBdROhypdQtUut0u4iSYs5A?sheet=tP5Np0');
        }
    }
}

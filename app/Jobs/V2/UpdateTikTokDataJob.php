<?php

namespace App\Jobs\V2;

use App\Services\V2\TiktokV2Service;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateTikTokDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $key, $sheetTitle, $date;
    private mixed $advertise;

    public function __construct($advertise, $date, $key = null, $sheetTitle = null)
    {
        $this->key = $key;
        $this->sheetTitle = $sheetTitle;
        $this->date = $date;
        $this->advertise = $advertise;
    }

    /**
     * @throws \Exception
     */
    public function handle(): void
    {
        // update for today
        \Log::info("[UpdateTikTokDataJob] Update TikTok Data for $this->date");
        $tiktokService = new TiktokV2Service();
        $tiktokService->updateOneAdvertise($this->advertise, $this->date, $this->key, $this->sheetTitle);
    }
}

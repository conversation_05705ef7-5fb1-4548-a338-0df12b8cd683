<?php


namespace App\Jobs\V2\Reports;

use App\Helpers\LogHelper;
use App\Models\AccountReport;
use App\Services\Reports\LarkSyncService;
use App\Services\V2\TikTokReportService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TikTokByDateRangeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected AccountReport $account;
    protected $startDate;
    protected $endDate;
    protected $url;

    public function __construct(AccountReport $account, $url, $startDate, $endDate)
    {
        $this->account = $account;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->url = $url;
    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        try {
            // Log job start
            LogHelper::writeLog(
                'TiktokByDateRangeJob',
                'info',
                'Job started',
                'stack',
                [
                    'advertiser_id' => $this->account->id ?? 'N/A',
                    'start_date' => $this->startDate,
                    'end_date' => $this->endDate
                ]);

            // Log step 1: Validate advertiser ID
            LogHelper::writeLog('TiktokByDateRangeJob', 'info', 'Validating advertiser ID');
            $advertiserId = $this->account->advertiser->advertiser_id
                ?? throw new Exception('Invalid advertiser ID');
            LogHelper::writeLog('TiktokByDateRangeJob', 'info', 'Advertiser ID validated', 'stack', [
                'advertiser_id' => $advertiserId
            ]);

            // Log step 3: Instantiate service class
            LogHelper::writeLog('TiktokByDateRangeJob', 'info', 'Instantiating service class');
            $service = new LarkSyncService();

            // Log step 4: Fetch report data
            LogHelper::writeLog('TiktokByDateRangeJob', 'info', 'Fetching TikTok report data');
            $tiktokReportService = new TikTokReportService();
            $type = $this->account->ad_format == 'tiktok_shop' ? 'basic' : 'gmv_max';
            $reportData = $tiktokReportService->getTotalSpendByDateRange(
                $advertiserId,
                $this->startDate,
                $this->endDate,
                $this->account,
                $type
            );

            // Early return if no report data
            if (!$reportData) {
                throw new Exception('Failed to fetch report data');
            }
            LogHelper::writeLog('TiktokByDateRangeJob', 'info', 'Report data fetched successfully', 'stack', [
                'total_reports' => count($reportData['daily_reports'] ?? [])
            ]);

            // Log step 5: Process daily reports
            $dailyReports = $reportData['daily_reports'] ?? [];

            // Sort daily reports by date in ascending order
            usort($dailyReports, function ($a, $b) {
                return strtotime($a['date']) - strtotime($b['date']);
            });

            LogHelper::writeLog('TiktokByDateRangeJob', 'info', 'Starting to process daily reports');

            foreach ($dailyReports as $index => $report) {
                // Log individual report processing
                LogHelper::writeLog('TiktokByDateRangeJob', 'info', 'Processing daily report', 'stack', [
                    'report_index' => $index,
                    'report_date' => $report['date'] ?? 'N/A'
                ]);

                // Validate report data
                $this->validateReportData($report);
                LogHelper::writeLog('TiktokByDateRangeJob', 'info', 'Report data validated');

                // Prepare and sync data
                $data = [
                    'date' => $report['date'],
                    'spend' => $report['spend'],
                ];

                // Attempt to sync data
                $this->syncReportData($service, $data);
                LogHelper::writeLog('TiktokByDateRangeJob', 'info', 'Report data synced', 'stack', [
                    'date' => $data['date'],
                    'spend' => $data['spend']
                ]);
            }

            // Log successful job completion
            LogHelper::writeLog('TiktokByDateRangeJob', 'info', 'Job completed successfully', 'stack', [
                'total_reports_processed' => count($dailyReports)
            ]);

        } catch (Exception $e) {
            // Centralized error logging
            LogHelper::writeLog(
                'TikTokByDateRangeJob',
                'error',
                "Job failed: {$e->getMessage()}",
                'stack',
                [
                    'trace' => $e->getTraceAsString(),
                    'advertiser_id' => $this->account->id ?? 'N/A',
                    'start_date' => $this->startDate,
                    'end_date' => $this->endDate
                ]
            );

            // Optionally rethrow for higher-level error handling
            throw $e;
        }
    }

    // Additional helper methods for improved code organization

    /**
     * @throws Exception
     */
    private function validateReportData(array $report): void
    {
        if (!isset($report['date']) || !isset($report['spend'])) {
            throw new Exception('Invalid report data structure');
        }
    }

    /**
     * @throws Exception
     */
    private function syncReportData(LarkSyncService $service, array $data): void
    {
        try {
            if (!$service->syncData($this->account, $data)) {
                throw new Exception("Sync failed for advertiser ID: {$this->account->id}");
            }
        } catch (Exception $e) {
            LogHelper::writeLog(
                'TikTokByDateRangeJob',
                'error',
                "Sync failed for advertiser ID: {$this->account->id}",
                'stack',
                [
                    'trace' => $e->getTraceAsString(),
                    'advertiser_id' => $this->account->id,
                    'data' => $data,
                    'message' => $e->getMessage()
                ]
            );
            throw $e;
        }
    }
}

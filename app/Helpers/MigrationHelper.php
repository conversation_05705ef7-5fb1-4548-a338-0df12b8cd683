<?php

namespace App\Helpers;

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MigrationHelper
{
    /**
     * Tạo bảng nếu chưa tồn tại
     *
     * @param string $tableName
     * @param \Closure $callback
     * @return void
     */
    public static function createTableIfNotExists($tableName, \Closure $callback)
    {
        if (!Schema::hasTable($tableName)) {
            Schema::create($tableName, $callback);
        }
    }

    /**
     * Tạo bảng hoặc cập nhật nếu đã tồn tại
     *
     * @param string $tableName
     * @param \Closure $callback
     * @return void
     */
    public static function createOrUpdateTable($tableName, \Closure $callback)
    {
        if (Schema::hasTable($tableName)) {
            Schema::table($tableName, $callback);
        } else {
            Schema::create($tableName, $callback);
        }
    }

    /**
     * X<PERSON>a bảng nếu tồn tại và tạo lại
     *
     * @param string $tableName
     * @param \Closure $callback
     * @return void
     */
    public static function recreateTable($tableName, \Closure $callback)
    {
        Schema::dropIfExists($tableName);
        Schema::create($tableName, $callback);
    }

    /**
     * Kiểm tra và tạo bảng với thông báo
     *
     * @param string $tableName
     * @param \Closure $callback
     * @return void
     */
    public static function safeCreateTable($tableName, \Closure $callback)
    {
        if (Schema::hasTable($tableName)) {
            echo "Bảng '{$tableName}' đã tồn tại, bỏ qua tạo bảng.\n";
            return;
        }

        Schema::create($tableName, $callback);
        echo "Đã tạo bảng '{$tableName}' thành công.\n";
    }
} 
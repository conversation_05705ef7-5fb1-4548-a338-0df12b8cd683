<?php

namespace App\Traits;

use App\Models\AccountReport;
use App\Models\LarkAuth;
use App\Services\Reports\ReportHandlerService;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use RuntimeException;

trait LarkTrait
{
    /**
     * Get the most appropriate Lark token, prioritizing user access token.
     *
     * @return string|null The most appropriate token or null if unable to get any token.
     */
    public function getLarkUserToken()
    {
        Log::channel('lark_daily')->info("[LarkTrait::getLarkUserToken] Getting latest Lark auth token");
        $larkAuth = LarkAuth::where('token_type', 'Bearer')->latest()->first();

        if ($larkAuth) {
            // Strategy 1: Check if user access token exists and is valid
            if (isset($larkAuth->access_token) && !$this->checkTokenExpiration($larkAuth, 'user_access_token')) {
                Log::channel('lark_daily')->info("[LarkTrait::getLarkUserToken] Using existing user_access_token");
                return $larkAuth->access_token;
            }

            // Strategy 2: Try to refresh user access token if refresh token exists and is valid
            if (isset($larkAuth->refresh_token) && !$this->checkTokenExpiration($larkAuth, 'refresh_token')) {
                Log::channel('lark_daily')->info("[LarkTrait::getLarkUserToken] Refreshing user token");
                $token = $this->refreshUserAccessToken($larkAuth);
                if ($token) {
                    return $token;
                }
            }

            // Strategy 3: Try to get a new user token via authorization if no valid refresh token
            // Note: This requires user interaction for the auth code, so we'll need a stored auth code
            // or a way to generate one programmatically
            $authCode = $this->getStoredOrGeneratedAuthCode();
            if ($authCode) {
                Log::channel('lark_daily')->info("[LarkTrait::getLarkUserToken] Getting new user token via auth code");
                $token = $this->getUserAccessTokenWithCode($authCode);
                if ($token) {
                    return $token;
                }
            }

            // Strategy 4: Fall back to tenant_access_token only if no user token can be obtained
            if (isset($larkAuth->tenant_access_token) && !$this->checkTokenExpiration($larkAuth)) {
                Log::channel('lark_daily')->info("[LarkTrait::getLarkUserToken] Falling back to existing tenant_access_token");
                return $larkAuth->tenant_access_token;
            }

            // Strategy 5: Get new app and tenant tokens as last resort
            Log::channel('lark_daily')->info("[LarkTrait::getLarkUserToken] Getting new app token as last resort");
            $tenant_access_token = $this->getAppAndTenantAccessToken(config('larksuite.app_id'), config('larksuite.app_secret'));
            if ($tenant_access_token['tenant_access_token']) {
                return $tenant_access_token['tenant_access_token'];
            }
        } else {
            // No auth record exists, create a new one with app and tenant tokens
            Log::channel('lark_daily')->info("[LarkTrait::getLarkUserToken] No auth record exists, creating new one");
            $tenant_access_token = $this->getAppAndTenantAccessToken(config('larksuite.app_id'), config('larksuite.app_secret'));

            // After getting tenant token, try to get user token if possible
            if ($tenant_access_token['tenant_access_token']) {
                $authCode = $this->getStoredOrGeneratedAuthCode();
                if ($authCode) {
                    $userToken = $this->getUserAccessTokenWithCode($authCode);
                    if ($userToken) {
                        return $userToken;
                    }
                }
                return $tenant_access_token['tenant_access_token'];
            }
        }

        Log::channel('lark_daily')->error("[LarkTrait::getLarkUserToken] Failed to get any valid token");
        return null;
    }

    /**
     * Gets a stored authorization code or generates one programmatically.
     * This is a placeholder method that should be implemented based on your application's needs.
     *
     * @return string|null An authorization code or null if not available
     */
    protected function getStoredOrGeneratedAuthCode(): ?string
    {
        // https://accounts.larksuite.com/open-apis/authen/v1/authorize?app_id=cli_a7539f311338902f&redirect_uri=https://tiktok.toge.com.vn&scope=sheets:spreadsheet%20drive:drive
        // Option 1: Return a stored auth code from database or config
        $storedCode = config('larksuite.stored_auth_code');
        if ($storedCode) {
            return $storedCode;
        }

        // Option 2: Implement a way to generate or retrieve a fresh auth code
        // This might involve redirecting the user to authorization URL
        // or using a pre-authorized service account

        // For now, return null as this needs specific implementation
        Log::channel('lark_daily')->info("[LarkTrait::getStoredOrGeneratedAuthCode] No auth code available");
        return null;
    }

    /**
     * Send an HTTP request with retry mechanism.
     *
     * @param string $method The HTTP method (e.g., GET, POST).
     * @param string $url The request URL.
     * @param array $headers The request headers.
     * @param string $body The request body.
     * @param int $maxRetries Maximum number of retry attempts.
     * @param int $baseDelay Base delay in seconds for exponential backoff.
     * @return array|null The response data or null if an error occurs.
     */
    protected function sendRequest(string $method, string $url, array $headers, string $body, int $maxRetries = null, int $baseDelay = null): ?array
    {
        // Use config values if not provided
        $maxRetries = $maxRetries ?? config('larksuite.retry.max_attempts', 3);
        $baseDelay = $baseDelay ?? config('larksuite.retry.base_delay', 2);

        $attempt = 0;
        $lastException = null;

        while ($attempt <= $maxRetries) {
            try {
                if ($attempt > 0) {
                    $delay = $this->calculateBackoffDelay($attempt, $baseDelay);
                    Log::channel('lark_daily')->info("[LarkTrait::sendRequest] Retrying request after delay", [
                        'attempt' => $attempt,
                        'delay_seconds' => $delay,
                        'url' => $url,
                        'method' => $method
                    ]);
                    sleep($delay);
                }

                $client = new Client([
                    'timeout' => config('larksuite.http.timeout', 60),
                    'connect_timeout' => config('larksuite.http.connect_timeout', 30),
                    'read_timeout' => config('larksuite.http.read_timeout', 60),
                ]);

                $request = new Request($method, $url, $headers, $body);
                $response = $client->send($request);
                $responseBody = $response->getBody()->getContents();

                Log::channel('lark_daily')->info("[LarkTrait::sendRequest] Response: " . $responseBody);

                $decodedResponse = json_decode($responseBody, true);

                // Check for Lark API specific retry conditions
                if ($this->shouldRetryLarkResponse($decodedResponse)) {
                    $attempt++;
                    $lastException = new RuntimeException("Lark API returned retry condition: " . json_encode($decodedResponse));

                    if ($attempt <= $maxRetries) {
                        Log::channel('lark_daily')->warning("[LarkTrait::sendRequest] Lark API retry condition detected", [
                            'attempt' => $attempt,
                            'response' => $decodedResponse,
                            'url' => $url
                        ]);
                        continue;
                    }
                }

                if ($attempt > 0) {
                    Log::channel('lark_daily')->info("[LarkTrait::sendRequest] Request succeeded after retry", [
                        'successful_attempt' => $attempt,
                        'url' => $url
                    ]);
                }

                return $decodedResponse;

            } catch (GuzzleException $e) {
                $lastException = $e;
                $attempt++;

                Log::channel('lark_daily')->warning("[LarkTrait::sendRequest] HTTP request failed", [
                    'attempt' => $attempt,
                    'max_retries' => $maxRetries,
                    'error' => $e->getMessage(),
                    'url' => $url,
                    'method' => $method
                ]);

                if ($attempt <= $maxRetries) {
                    continue;
                }
            }
        }

        // All retries exhausted
        Log::channel('lark_daily')->error("[LarkTrait::sendRequest] All retry attempts exhausted", [
            'max_retries' => $maxRetries,
            'last_error' => $lastException ? $lastException->getMessage() : 'Unknown error',
            'url' => $url,
            'method' => $method
        ]);

        return null;
    }

    /**
     * Check if Lark API response indicates a retry condition.
     *
     * @param array|null $response The API response
     * @return bool True if should retry, false otherwise
     */
    protected function shouldRetryLarkResponse(?array $response): bool
    {
        if (!$response) {
            return false;
        }

        // Get retryable codes from config
        $httpRetryableCodes = config('larksuite.retryable_codes.http', [429, 500, 502, 503, 504]);
        $larkRetryableCodes = config('larksuite.retryable_codes.lark_api', [90235, 1310235]);
        $retryableCodes = array_merge($httpRetryableCodes, $larkRetryableCodes);

        if (isset($response['code']) && in_array($response['code'], $retryableCodes)) {
            return true;
        }

        // Get retryable messages from config
        $retryableMessages = config('larksuite.retryable_messages', [
            'data not ready,retry later',
            'retry later',
            'rate limit exceeded',
            'service temporarily unavailable'
        ]);

        if (isset($response['msg'])) {
            $message = strtolower($response['msg']);
            foreach ($retryableMessages as $retryableMessage) {
                if (strpos($message, strtolower($retryableMessage)) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Calculate exponential backoff delay with jitter.
     *
     * @param int $attempt Current attempt number (1-based)
     * @param int $baseDelay Base delay in seconds
     * @return int Delay in seconds
     */
    protected function calculateBackoffDelay(int $attempt, int $baseDelay = 2): int
    {
        $useExponentialBackoff = config('larksuite.retry.exponential_backoff', true);
        $useJitter = config('larksuite.retry.jitter', true);
        $maxDelay = config('larksuite.retry.max_delay', 120);

        if ($useExponentialBackoff) {
            // Exponential backoff: baseDelay * (2^(attempt-1))
            $delay = $baseDelay * (2 ** ($attempt - 1));
        } else {
            // Linear backoff: baseDelay * attempt
            $delay = $baseDelay * $attempt;
        }

        // Add random jitter (0-50% of the delay) to avoid thundering herd
        if ($useJitter) {
            $jitter = random_int(0, (int)($delay * 0.5));
            $delay += $jitter;
        }

        // Cap maximum delay
        return min($delay, $maxDelay);
    }

    /**
     * Obtains both app and tenant access tokens from Lark.
     *
     * @param string $appId The application ID.
     * @param string $appSecret The application secret.
     * @return array|null The tenant access token, or null if an error occurs.
     */
    public function getAppAndTenantAccessToken(string $appId, string $appSecret): array|null
    {
        $headers = ['Content-Type' => 'application/json'];
        $body = json_encode(['app_id' => $appId, 'app_secret' => $appSecret]);
        $response = $this->sendRequest('POST', 'https://open.larksuite.com/open-apis/auth/v3/app_access_token/internal', $headers, $body);

        Log::channel('lark_daily')->info("[LarkTrait::getAppAndTenantAccessToken] Response: " . json_encode($response));

        if (isset($response['tenant_access_token'])) {
            LarkAuth::updateOrCreate(
                ['app_id' => $appId],
                [
                    'tenant_access_token' => $response['tenant_access_token'],
                    'app_access_token' => $response['app_access_token'] ?? null,
                    'expire' => $response['expire'] ?? 7200,
                    'created_at' => Carbon::now('asia/ho_chi_minh'),
                    'updated_at' => Carbon::now('asia/ho_chi_minh'),
                ]
            );
            return [
                'app_access_token' => $response['app_access_token'],
                'tenant_access_token' => $response['tenant_access_token'],
            ];
        } else {
            Log::channel('lark_daily')->error("[LarkTrait::getAppAndTenantAccessToken] Failed to get tenant_access_token");
            return null;
        }
    }

    /**
     * Obtains a user access token from Lark using an authorization code.
     *
     * @param string $authCode The authorization code from Lark
     * @return string|null The user access token, or null if an error occurs.
     */
    public function getUserAccessTokenWithCode(string $authCode, bool $getNewAppToken = true): ?string
    {
        // First get a valid app_access_token or tenant_access_token
        $tenant_access_token = null;
        if ($getNewAppToken) {
            $appToken = $this->getAppAndTenantAccessToken(config('larksuite.app_id'), config('larksuite.app_secret'));
            if ($appToken['app_access_token']) {
                $appToken = $appToken['app_access_token'];
            }
            if ($appToken['tenant_access_token']) {
                $tenant_access_token = $appToken['tenant_access_token'];
            }
        } else {
            $appTokenByAuthRecord = LarkAuth::latest()->first();
            if (!$appTokenByAuthRecord) {
                Log::channel('lark_daily')->error("[LarkTrait::getUserAccessTokenWithCode] No valid app token found");
                return null;
            }
            $appToken = $appTokenByAuthRecord->app_access_token;
            $tenant_access_token = $appTokenByAuthRecord->tenant_access_token;
        }

        if (empty($appToken)) {
            Log::channel('lark_daily')->error("[LarkTrait::getUserAccessTokenWithCode] Failed to get app token");
            return null;
        }

        $headers = [
            'Content-Type' => 'application/json; charset=utf-8',
            'Authorization' => "Bearer {$appToken}",
        ];

        $body = json_encode([
            'grant_type' => 'authorization_code',
            'code' => $authCode,
        ]);

        $response = $this->sendRequest('POST', 'https://open.larksuite.com/open-apis/authen/v1/oidc/access_token', $headers, $body);
        Log::channel('lark_daily')->info("[LarkTrait::getUserAccessTokenWithCode] Response: " . json_encode($response));

        if (isset($response['code']) && $response['code'] == 0 && isset($response['data']['access_token'])) {
            $responseData = $response['data'];

            LarkAuth::updateOrCreate(
                ['app_id' => config('larksuite.app_id')],
                [
                    'tenant_access_token' => $tenant_access_token,
                    'app_access_token' => $appToken,
                    'access_token' => $responseData['access_token'],
                    'refresh_token' => $responseData['refresh_token'],
                    'token_type' => $responseData['token_type'],
                    'expires_in' => $responseData['expires_in'],
                    'refresh_expires_in' => $responseData['refresh_expires_in'],
                    'created_at' => Carbon::now('asia/ho_chi_minh'),
                    'updated_at' => Carbon::now('asia/ho_chi_minh'),
                ]
            );

            return $responseData['access_token'];
        } else {
            Log::channel('lark_daily')->error("[LarkTrait::getUserAccessTokenWithCode] Failed to get user access token: " . json_encode($response));
            return null;
        }
    }

    /**
     * Checks if the token has expired.
     *
     * @param LarkAuth $token The token object.
     * @param string $type The type of token to check.
     * @return bool True if the token has expired, false otherwise.
     */
    public function checkTokenExpiration(LarkAuth $token, string $type = 'tenant_access_token'): bool
    {
        $now = Carbon::now('asia/ho_chi_minh');

        switch ($type) {
            case 'tenant_access_token':
                if (empty($token->tenant_access_token) || empty($token->created_at) || empty($token->expire)) {
                    return true;
                }
                $expirationTime = Carbon::parse($token->created_at, 'asia/ho_chi_minh')->addSeconds($token->expire);
                break;

            case 'user_access_token':
                if (empty($token->access_token) || empty($token->created_at) || empty($token->expires_in)) {
                    return true;
                }
                $expirationTime = Carbon::parse($token->created_at, 'asia/ho_chi_minh')->addSeconds($token->expires_in);
                break;

            case 'refresh_token':
                if (empty($token->refresh_token) || empty($token->created_at) || empty($token->refresh_expires_in)) {
                    return true;
                }
                $expirationTime = Carbon::parse($token->created_at, 'asia/ho_chi_minh')->addSeconds($token->refresh_expires_in);
                break;

            default:
                return true;
        }

        // Add buffer of 5 minutes to be safe
        $expirationTime = $expirationTime->subMinutes(5);

        $isExpired = $now->greaterThanOrEqualTo($expirationTime);
        Log::channel('lark_daily')->info("[LarkTrait::checkTokenExpiration] Token type {$type} " .
            ($isExpired ? "has expired" : "is still valid") .
            " (expires at {$expirationTime}, now is {$now})");

        return $isExpired;
    }

    /**
     * Refreshes the user access token using a refresh token.
     *
     * @param LarkAuth $larkAuth
     * @return string|null The new access token, or null if an error occurs.
     */
    public function refreshUserAccessToken(LarkAuth $larkAuth): ?string
    {
        // Validate refresh token exists
        if (empty($larkAuth->refresh_token)) {
            Log::channel('lark_daily')->error("[LarkTrait::refreshUserAccessToken] No refresh token available");
            return null;
        }

        // Check if tenant_access_token is valid
        if (!empty($larkAuth->tenant_access_token) && !$this->checkTokenExpiration($larkAuth)) {
            $appToken = $larkAuth->tenant_access_token;
        } else {
            // Get new app token if needed
            $appToken = $this->getAppAndTenantAccessToken(config('larksuite.app_id'), config('larksuite.app_secret'));
            if ($appToken['app_access_token']) {
                $appToken = $appToken['app_access_token'];
            }
        }

        if (empty($appToken)) {
            Log::channel('lark_daily')->error("[LarkTrait::refreshUserAccessToken] Failed to get valid app token");
            return null;
        }

        Log::channel('lark_daily')->info("[LarkTrait::refreshUserAccessToken] Using app token: {$appToken}");

        $headers = [
            'Content-Type' => 'application/json; charset=utf-8',
            'Authorization' => "Bearer {$appToken}",
        ];

        $body = json_encode([
            'grant_type' => 'refresh_token',
            'refresh_token' => $larkAuth->refresh_token,
        ]);

        $response = $this->sendRequest('POST', 'https://open.larksuite.com/open-apis/authen/v1/oidc/refresh_access_token', $headers, $body);
        Log::channel('lark_daily')->info("[LarkTrait::refreshUserAccessToken] Response: " . json_encode($response));

        if (isset($response['code']) && $response['code'] == 0 && isset($response['data']['access_token'])) {
            $responseData = $response['data'];

            // Update the auth record
            LarkAuth::updateOrCreate(
                ['app_id' => config('larksuite.app_id')],
                [
                    'tenant_access_token' => $appToken,
                    'access_token' => $responseData['access_token'],
                    'refresh_token' => $responseData['refresh_token'],
                    'token_type' => $responseData['token_type'],
                    'expires_in' => $responseData['expires_in'],
                    'refresh_expires_in' => $responseData['refresh_expires_in'],
                    'created_at' => Carbon::now('asia/ho_chi_minh'),
                    'updated_at' => Carbon::now('asia/ho_chi_minh'),
                ]
            );

            return $responseData['access_token'];
        } else {
            Log::channel('lark_daily')->error("[LarkTrait::refreshUserAccessToken] Failed to refresh token: " . json_encode($response));
            return null;
        }
    }

    /**
     * Check token validity and refresh if needed
     *
     * @param string|null $token Current token to check
     * @return string|null Valid token or null if unable to get one
     */
    protected function ensureValidToken(?string $token = null): ?string
    {
        // If no token provided, get one
        if (!$token) {
            return $this->getLarkUserToken();
        }

        // Check if token is in our database
        $larkAuth = LarkAuth::where('access_token', $token)
            ->orWhere('tenant_access_token', $token)
            ->latest()
            ->first();

        if (!$larkAuth) {
            Log::channel('lark_daily')->warning("[LarkTrait::ensureValidToken] Token not found in database, getting new token");
            return $this->getLarkUserToken();
        }

        // If it's a user token
        if ($larkAuth->access_token === $token) {
            if (!$this->checkTokenExpiration($larkAuth, 'user_access_token')) {
                return $token; // Still valid
            }

            // Try to refresh
            Log::channel('lark_daily')->info("[LarkTrait::ensureValidToken] User token expired, trying to refresh");
            if (!$this->checkTokenExpiration($larkAuth, 'refresh_token')) {
                $newToken = $this->refreshUserAccessToken($larkAuth);
                if ($newToken) {
                    return $newToken;
                }
            }
        }

        // If it's a tenant token
        if ($larkAuth->tenant_access_token === $token) {
            if (!$this->checkTokenExpiration($larkAuth)) {
                return $token; // Still valid
            }
        }

        // Get a new token if we couldn't validate or refresh existing one
        return $this->getLarkUserToken();
    }

    /**
     * Append values to a Lark Suite spreadsheet with enhanced retry mechanism.
     *
     * @param string $accessToken The access token.
     * @param string $spreadsheetKey The spreadsheet key.
     * @param string $range The range.
     * @param array $values The values to append.
     * @param int $maxRetries Maximum number of retry attempts (default: 3).
     * @param int $baseDelay Base delay in seconds for exponential backoff (default: 3).
     * @return array The response content, or empty array if an error occurs.
     */
    public function appendValuesToSheet(string $accessToken, string $spreadsheetKey, string $range, array $values, int $maxRetries = null, int $baseDelay = null): array
    {
        // Ensure we have a valid token
        $validToken = $this->ensureValidToken($accessToken);
        if (!$validToken) {
            Log::channel('lark_daily')->error("[LarkTrait::appendValuesToSheet] Failed to get valid token");
            return [];
        }

        $headers = [
            'Authorization' => 'Bearer ' . $validToken,
            'Content-Type' => 'application/json',
        ];

        $body = json_encode([
            'valueRange' => [
                'range' => $range,
                'values' => $values,
            ],
        ]);

        $url = "https://open.larksuite.com/open-apis/sheets/v2/spreadsheets/{$spreadsheetKey}/values_append";
        Log::channel('lark_daily')->info("[LarkTrait::appendValuesToSheet] Sending POST request to $url with body: $body");

        // Use config defaults if not provided
        $maxRetries = $maxRetries ?? config('larksuite.sheets.max_retries', 3);
        $baseDelay = $baseDelay ?? config('larksuite.sheets.base_delay', 3);

        // Use enhanced sendRequest with retry mechanism
        return $this->sendRequest('POST', $url, $headers, $body, $maxRetries, $baseDelay) ?? [];
    }

    /**
     * Update cells in a Lark Suite spreadsheet with enhanced retry mechanism.
     *
     * @param string $accessToken The access token.
     * @param string $spreadsheetKey The spreadsheet key.
     * @param string $range The range.
     * @param array $values The values to update.
     * @param int $maxRetries Maximum number of retry attempts (default: 3).
     * @param int $baseDelay Base delay in seconds for exponential backoff (default: 3).
     * @return array The response content, or empty array if an error occurs.
     */
    public function updateCells($accessToken, $spreadsheetKey, $range, $values, int $maxRetries = null, int $baseDelay = null): array
    {
        // Ensure we have a valid token
        $validToken = $this->ensureValidToken($accessToken);
        if (!$validToken) {
            Log::channel('lark_daily')->error("[LarkTrait::updateCells] Failed to get valid token");
            return [];
        }

        $headers = [
            'Authorization' => 'Bearer ' . $validToken,
            'Content-Type' => 'application/json',
        ];

        $body = json_encode([
            'valueRange' => [
                'range' => $range,
                'values' => $values,
            ],
        ]);
        $url = "https://open.larksuite.com/open-apis/sheets/v2/spreadsheets/{$spreadsheetKey}/values";
        Log::channel('lark_daily')->info("[LarkTrait::updateCells] Sending PUT request to $url with body: $body");

        // Use config defaults if not provided
        $maxRetries = $maxRetries ?? config('larksuite.sheets.max_retries', 3);
        $baseDelay = $baseDelay ?? config('larksuite.sheets.base_delay', 3);

        // Use enhanced sendRequest with retry mechanism
        return $this->sendRequest('PUT', $url, $headers, $body, $maxRetries, $baseDelay) ?? [];
    }

    /**
     * Get information about a sheet in a Lark Suite spreadsheet.
     *
     * @param string $lark_auth_token The access token.
     * @param string $spreadsheet_Key The spreadsheet key.
     * @param string $sheet_Key The sheet key.
     * @return string|null The response as a JSON string, or null if an error occurs.
     */
    public function getInfoSheet(string $lark_auth_token, string $spreadsheet_Key, string $sheet_Key): ?string
    {
        try {
            // Ensure we have a valid token
            $validToken = $this->ensureValidToken($lark_auth_token);
            if (!$validToken) {
                throw new Exception("Failed to get valid token");
            }

            // Log all parameters
            Log::channel('lark_daily')->info("[LarkTrait::getInfoSheet] Parameters:", [
                'spreadsheet_Key' => $spreadsheet_Key,
                'sheet_Key' => $sheet_Key,
                'lark_auth_token' => substr($validToken, 0, 10) . '...', // Only log partial token for security
            ]);

            $url = "https://open.larksuite.com/open-apis/sheets/v3/spreadsheets/{$spreadsheet_Key}/sheets/{$sheet_Key}";
            Log::channel('lark_daily')->info("[LarkTrait::getInfoSheet] Sending GET request to $url");

            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => [
                    "Authorization: Bearer {$validToken}",
                ],
            ]);

            $response = curl_exec($curl);
            $err = curl_error($curl);
            curl_close($curl);

            if ($err) {
                Log::channel('lark_daily')->error("[LarkTrait::getInfoSheet] cURL Error: $err");
                return null;
            }

            Log::channel('lark_daily')->info("[LarkTrait::getInfoSheet] Response: $response");
            return $response;
        } catch (Exception $e) {
            Log::channel('lark_daily')->error("[LarkTrait::getInfoSheet] Exception: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Find cells containing a specific keyword in a Lark Suite spreadsheet with enhanced retry mechanism.
     *
     * @param string $lark_auth_token The access token.
     * @param string $spreadsheet_Key The spreadsheet key.
     * @param string $sheet_Key The sheet key.
     * @param string $keyword The keyword to search for.
     * @param string $range The range to search within.
     * @param int $maxRetries Maximum number of retry attempts (default: 3).
     * @param int $baseDelay Base delay in seconds for exponential backoff (default: 3).
     * @return array|null The response content, or null if an error occurs.
     */
    public function findCells(string $lark_auth_token, string $spreadsheet_Key, string $sheet_Key, string $keyword, string $range, int $maxRetries = null, int $baseDelay = null): ?array
    {
        // Ensure we have a valid token
        $validToken = $this->ensureValidToken($lark_auth_token);
        if (!$validToken) {
            Log::channel('lark_daily')->error("[LarkTrait::findCells] Failed to get valid token");
            return null;
        }

        $url = "https://open.larksuite.com/open-apis/sheets/v3/spreadsheets/{$spreadsheet_Key}/sheets/{$sheet_Key}/find";
        $headers = [
            'Content-Type' => 'application/json; charset=utf-8',
            'Authorization' => "Bearer {$validToken}",
        ];
        $body = json_encode([
            "find" => $keyword,
            "find_condition" => [
                "include_formulas" => false,
                "match_case" => true,
                "match_entire_cell" => false,
                "range" => $range,
                "search_by_regex" => false,
            ],
        ]);

        try {
            // Use config defaults if not provided
            $maxRetries = $maxRetries ?? config('larksuite.sheets.max_retries', 3);
            $baseDelay = $baseDelay ?? config('larksuite.sheets.base_delay', 3);

            Log::channel('lark_daily')->info("[LarkTrait::findCells] Sending POST request to $url with body: $body");
            // Use enhanced sendRequest with retry mechanism
            $response = $this->sendRequest('POST', $url, $headers, $body, $maxRetries, $baseDelay);
            Log::channel('lark_daily')->info("[LarkTrait::findCells] Response received: " . json_encode($response));
            return $response;
        } catch (Exception $e) {
            Log::channel('lark_daily')->error("[LarkTrait::findCells] Error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Read a single range from a Lark Suite spreadsheet.
     *
     * @param string $lark_auth_token The access token.
     * @param string $spreadsheet_Key The spreadsheet key.
     * @param string $sheet_Key The sheet key.
     * @param string $range The range to read.
     * @return array|null The response content, or null if an error occurs.
     */
    public function readSingleRange(string $lark_auth_token, string $spreadsheet_Key, string $sheet_Key, string $range)
    {
        // Ensure we have a valid token
        $validToken = $this->ensureValidToken($lark_auth_token);
        if (!$validToken) {
            Log::channel('lark_daily')->error("[LarkTrait::readSingleRange] Failed to get valid token");
            return null;
        }

        $url = "https://open.larksuite.com/open-apis/sheets/v2/spreadsheets/{$spreadsheet_Key}/values/{$sheet_Key}!{$range}";
        try {
            Log::channel('lark_daily')->info("[LarkTrait::readSingleRange] Sending GET request to $url");

            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => [
                    "Authorization: Bearer {$validToken}",
                ],
            ]);

            $response = curl_exec($curl);
            $err = curl_error($curl);
            curl_close($curl);

            if ($err) {
                Log::channel('lark_daily')->error("[LarkTrait::readSingleRange] cURL Error: $err");
                return null;
            }

            // Log::channel('lark_daily')->info("[LarkTrait::readSingleRange] Response received: $response");
            return json_decode($response, true);
        } catch (Exception $e) {
            Log::channel('lark_daily')->error("[LarkTrait::readSingleRange] Error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Prepare data for insertion into a Lark Suite spreadsheet.
     *
     * @param string $day The day for the report.
     * @param array $data_report The report data.
     * @return array The prepared data.
     */
    public function prepareLarkData($day, $data_report)
    {
        \Log::info("[prepareLarkData] Processing report for day: $day");

        // Ensure data_report is in the correct format
        if (!is_array($data_report)) {
            \Log::warning("[prepareLarkData] data_report is not an array. Converting to array.");
            $data_report = json_decode(json_encode($data_report), true);
        }

        \Log::info("[prepareLarkData] Initial data_report:", $data_report);

        // Initialize metrics
        $totals = [
            'ADS_budget' => 0,
            'actual_ADS' => 0,
            'order' => 0,
            'ROAS' => 0,
            'gross_revenue' => 0,
            'cost_per_conversion' => 0,
            'conversion' => 0,
        ];

        // Handle single report case
        if (isset($data_report['metrics'])) {
            $data_report = [$data_report];
        }

        // Process each report
        foreach ($data_report as $index => $item) {
            \Log::info("[prepareLarkData] Processing item #$index:", $item);

            // Get metrics safely
            $metrics = $item['metrics'] ?? [];
            if (!is_array($metrics) || empty($metrics)) {
                \Log::warning("[prepareLarkData] Invalid metrics format in item #$index. Skipping.");
                continue;
            }

            // Extract and convert metrics with safe defaults
            $spend = floatval($metrics['spend'] ?? 0);
            $onsite_shopping = intval($metrics['onsite_shopping'] ?? 0);
            $onsite_shopping_roas = floatval($metrics['onsite_shopping_roas'] ?? 0);
            $total_onsite_shopping_value = floatval($metrics['total_onsite_shopping_value'] ?? 0);
            $item_cost_per_conversion = floatval($metrics['cost_per_conversion'] ?? 0);
            $item_conversion = intval($metrics['conversion'] ?? 0);

            // Accumulate totals
            $totals['ADS_budget'] += $spend;
            $totals['actual_ADS'] += $spend + ($spend * 0.05); // 5% additional cost
            $totals['order'] += $onsite_shopping;
            $totals['ROAS'] += $onsite_shopping_roas;
            $totals['gross_revenue'] += $total_onsite_shopping_value;
            $totals['cost_per_conversion'] += $item_cost_per_conversion;
            $totals['conversion'] += $item_conversion;

            \Log::info("[prepareLarkData] Accumulated totals after item #$index:", $totals);
        }

        // Prepare final result
        $result = array_merge(['day' => $day], $totals);

        \Log::info("[prepareLarkData] Final result:", $result);

        return $result;
    }

    /**
     * @throws Exception
     */
    protected function handleFetchReportToLark(AccountReport $account, $advertiserId, array $data, string $url_sheet = null, string $type = 'tiktok_shop')
    {
        $log = Log::channel('campaign_daily');
        try {
            $log->info("[TikTokReportService::handleFetchReportToLark] Starting process for advertiser: {$advertiserId}", [
                'data' => $data,
                'account_id' => $account->id,
                'type' => $type,
            ]);

            // Get sheet details from URL
            $url_sheet = $url_sheet ?? $account->link_lark_sheet;
            if (!preg_match('/\/(?:sheets\/|wiki\/)([^?\/]+)\?sheet=([^&]+)/', $url_sheet, $url_sheet_array)) {
                throw new InvalidArgumentException("Invalid sheet URL format: {$url_sheet}");
            }

            $spreadsheet_key = $url_sheet_array[1];
            $sheet_key = $url_sheet_array[2];

            // Get Lark authentication token
            $lark_auth_token = $this->getLarkUserToken();
            if (empty($lark_auth_token)) {
                throw new RuntimeException("Failed to obtain Lark authentication token");
            }

            $reportHandlerService = new ReportHandlerService();

            // append value to lark sheet
            $query_sheet = $this->getInfoSheet($lark_auth_token, $spreadsheet_key, $sheet_key);
            $row_count = $reportHandlerService->validateSheetResponse($query_sheet, $advertiserId);

            // Check for existing data with the same date
            $range_find = "{$sheet_key}!A1:A{$row_count}";
            $find_result = $this->findCells($lark_auth_token, $spreadsheet_key, $sheet_key, $data['day'], $range_find);

            [$is_duplicate, $row_count, $data] = $reportHandlerService->processExistingData(
                $find_result,
                $row_count,
                $data,
                $lark_auth_token,
                $spreadsheet_key,
                $sheet_key,
                $type
            );

            // Prepare values for insertion/update
            $exchange_rate = (int)$account->exchange_rate;
            $values = $reportHandlerService->prepareSheetValues($account, $data, $row_count, $exchange_rate, $is_duplicate);

            $range = "{$sheet_key}!A{$row_count}:L{$row_count}";

            // Update or append values based on duplicate status
            if ($is_duplicate) {
                $this->updateCells($lark_auth_token, $spreadsheet_key, $range, $values);
                $log->info("[TikTokReportService::handleFetchReportToLark] Updated existing row", [
                    'row' => $row_count,
                    'advertiser_id' => $advertiserId,
                ]);
            } else {
                $this->appendValuesToSheet($lark_auth_token, $spreadsheet_key, $range, $values);
                $log->info("[TikTokReportService::handleFetchReportToLark] Appended new row", [
                    'row' => $row_count,
                    'advertiser_id' => $advertiserId,
                ]);
            }

        } catch (Exception $e) {
            $log->error("[TikTokReportService::handleFetchReportToLark] Error processing report", [
                'error' => $e->getMessage(),
                'advertiser_id' => $advertiserId,
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }
}

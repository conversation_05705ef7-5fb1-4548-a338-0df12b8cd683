<?php

namespace App\Filament\Resources\CustomerReportAccountResource\Actions;

use App\Models\CustomerReportAccount;
use App\Services\V2\Tiktok\Sheet\CustomerReport\CustomerReportSheetService;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class DeleteSheetAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'deleteSheet';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->icon('heroicon-s-trash')
            ->label('Xóa Sheet')
            ->color('danger')
            ->modalHeading('Xóa')
            ->modalDescription('Xóa dữ liệu Google Sheet và dừng chạy')
            ->modalIcon('heroicon-s-trash')
            ->action(function (CustomerReportAccount $record): void {
                try {
                    $service = app(CustomerReportSheetService::class);
                    $service->deleteSpreadsheet($record);

                    Notification::make()
                        ->title('Xóa spreadsheet thành công')
                        ->body("Đã xóa spreadsheet cho báo cáo {$record->customerReport->report_name}")
                        ->success()
                        ->send();
                } catch (\Exception $e) {
                    Notification::make()
                        ->title('Lỗi khi xóa spreadsheet')
                        ->body($e->getMessage())
                        ->danger()
                        ->send();
                }
            })
            ->visible(fn(CustomerReportAccount $record) => $record->is_sheet)
            ->requiresConfirmation();
    }
} 
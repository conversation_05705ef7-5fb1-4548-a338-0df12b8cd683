<?php

namespace App\Services\Reports;

use App\Models\TikTokShopAdvertiserGmvSheet;
use App\Traits\LarkTrait;
use Illuminate\Log\Logger;
use InvalidArgumentException;
use Log;
use RuntimeException;
use Throwable;

class TikTokShopAdvertiserReportService extends BaseReportService
{
    use LarkTrait;

    /**
     * Generate report for a specific advertiser
     * @throws Throwable
     */
    public function generateReportForAdvertiser(TikTokShopAdvertiserGmvSheet $advertiser, string $reportDate): void
    {
        $log = Log::channel('report_daily');

        if (!isset($advertiser->campaignsWithLarkReporting)) {
            throw new RuntimeException('Invalid advertiser data: missing campaigns');
        }

        $campaigns = $advertiser->campaignsWithLarkReporting;

        if ($campaigns->isEmpty()) {
            $log->error("[ReportService][$reportDate] No campaigns found for advertiser {$advertiser->id} - {$advertiser->name}");
            return;
        }

        // Filter campaigns by status
        $campaigns = $campaigns->filter(function ($campaign) {
            return $campaign->primary_status === 'STATUS_DELIVERY_OK'
                && $campaign->operation_status === 'ENABLE';
        });

        if ($campaigns->isEmpty()) {
            $log->error("[ReportService][$reportDate] No active campaigns found for advertiser {$advertiser->id} - {$advertiser->name}");
            return;
        }

        $log->info("[ReportService][$reportDate] Processing advertiser {$advertiser->id} - {$advertiser->name} with " . $campaigns->count() . " active campaigns");

        try {
            $this->generateAdvertiserReports($advertiser, $campaigns, $reportDate, $log);
        } catch (Throwable $e) {
            $log->error("[ReportService][$reportDate] Failed to process advertiser {$advertiser->id} - {$advertiser->name}: " . $e->getMessage());
            throw $e;
        }
    }

    private function generateAdvertiserReports(TikTokShopAdvertiserGmvSheet $advertiser, $campaigns, string $today, $log): void
    {
        $text_for_gmv = $this->getDefaultGMVData($today);

        [$spreadsheet_key, $sheet_key] = $this->extractKeysFromUrl($advertiser->lark_sheet_link);
        $lark_auth_token = $this->getLarkUserToken();

        $row_count = $this->getRowCount($lark_auth_token, $spreadsheet_key, $sheet_key);

        $report_data = $this->processReportsByType(
            $advertiser,
            $campaigns,
            $today,
            $lark_auth_token,
            $spreadsheet_key,
            $sheet_key,
            $row_count,
            $text_for_gmv,
            $log
        );

        $this->updateSheets($advertiser, $report_data, $lark_auth_token, $spreadsheet_key, $sheet_key, $log, $today);
    }

    private function processReportsByType(
        TikTokShopAdvertiserGmvSheet $advertiser,
               $campaigns,
        string $today,
        string $lark_auth_token,
        string $spreadsheet_key,
        string $sheet_key,
        int    $row_count,
        array  $text_for_gmv,
        Logger $log
    ): array
    {
        $report_data = [
            'gmv' => [
                'is_duplicate' => false,
                'row' => $row_count,
                'data' => $text_for_gmv,
            ],
        ];

        if ($row_count >= self::MIN_ROW_COUNT) {
            $report_data = $this->checkExistingEntries(
                $report_data,
                $today,
                $lark_auth_token,
                $spreadsheet_key,
                $sheet_key,
                $row_count,
                $log
            );
        }

        foreach ($campaigns as $campaign) {
            $report_data['gmv']['data'] = $this->updateGMVData(
                $report_data['gmv']['data'],
                $campaign,
                $report_data['gmv']['row']
            );
        }

        return $report_data;
    }

    protected function checkExistingEntries(array $report_data, string $today, string $lark_auth_token, string $spreadsheet_key, string $sheet_key, int $row_count, Logger $log): array
    {
        $start_row = max(self::DEFAULT_RANGE_START, 3);

        $range_find_day_of_gmv = "{$sheet_key}!A{$start_row}:A";
        $find_gmv = $this->findCells($lark_auth_token, $spreadsheet_key, $sheet_key, $today, $range_find_day_of_gmv);
        [$report_data['gmv']['is_duplicate'], $report_data['gmv']['row']] =
            $this->checkDuplicate($find_gmv, $row_count, $log, 'gmv');

        if (!$report_data['gmv']['is_duplicate']) {
            $report_data['gmv']['row'] = max($report_data['gmv']['row'] + 1, 3);
        }

        return $report_data;
    }

    protected function updateSheets($entity, array $reportData, string $lark_auth_token, string $spreadsheet_key, string $sheet_key, Logger $log, string $today): void
    {
        $existingData = $this->readValuesFromSheet($lark_auth_token, $spreadsheet_key, $sheet_key . '!A:A');
        $existingDates = [];
        if (isset($existingData['data']['valueRange']['values'])) {
            foreach ($existingData['data']['valueRange']['values'] as $row) {
                if (!empty($row[0])) {
                    $existingDates[] = $row[0];
                }
            }
        }

        $gmv_row = $reportData['gmv']['row'] === 2 ? 3 : $reportData['gmv']['row'];
        $gmv_value_col = 'G';
        $gmv_cost_col = 'E';
        $reportData['gmv']['data']['roi_trung_binh'] = "= IFERROR(ROUND({$gmv_value_col}{$gmv_row} / {$gmv_cost_col}{$gmv_row}, 2), 0)";

        $dateIndex = array_search($today, $existingDates);
        if ($dateIndex !== false) {
            $rowNumber = $dateIndex + 1;
            $range = $sheet_key . '!A' . $rowNumber . ':J' . $rowNumber;
            $formattedData = $this->formatGMVData($reportData['gmv']['data']);
            $this->updateValuesInSheet($lark_auth_token, $spreadsheet_key, $range, $formattedData);
            $log->info("[ReportService] Updated GMV data at row {$rowNumber}");
        } else {
            $this->updateSheet(
                $reportData['gmv']['data'],
                'gmv',
                $gmv_row,
                false,
                [
                    'token' => $lark_auth_token,
                    'spreadsheet_key' => $spreadsheet_key,
                    'sheet_key' => $sheet_key,
                    'log' => $log,
                    'today' => $today,
                    'advertiser' => $entity,
                ]
            );
            $log->info("[ReportService] Appended new GMV data at row {$gmv_row}");
        }
    }

    private function updateSheet(array $data, string $type, int $row, bool $is_duplicate, array $params): void
    {
        $column_mapping = [
            'gmv' => ['start' => 'A', 'offset' => 'J', 'format_method' => 'formatGMVData'],
        ];

        if (!isset($column_mapping[$type])) {
            Log::error("Invalid type provided: {$type}");
            throw new InvalidArgumentException("Invalid type: {$type}");
        }

        $start_column = $column_mapping[$type]['start'];
        $start_index = getColumnIndex($start_column);
        $offset_index = getColumnIndex($column_mapping[$type]['offset']);
        $end_column = getColumnLetter($offset_index);
        $range = "{$params['sheet_key']}!{$start_column}$row:{$end_column}$row";

        $format_method = $column_mapping[$type]['format_method'];
        $formatted_data = $this->$format_method($data);

        if ($is_duplicate) {
            $this->updateCells(
                $params['token'],
                $params['spreadsheet_key'],
                $range,
                $formatted_data
            );
        } else {
            $this->appendValuesToSheet(
                $params['token'],
                $params['spreadsheet_key'],
                $range,
                $formatted_data
            );
        }

        $params['log']->info(
            '[ReportService] ' .
            ($is_duplicate ? 'Updated' : 'Appended') .
            " $type data to sheet $range on {$params['today']}"
        );
    }
}

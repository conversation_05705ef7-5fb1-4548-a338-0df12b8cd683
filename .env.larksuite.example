# Lark Suite API Configuration
# Copy these settings to your .env file and adjust values as needed

# Basic Authentication
LARK_APP_ID=your_app_id_here
LARK_APP_SECRET=your_app_secret_here
LARK_CHAT_ID=your_chat_id_here
LARK_DEFAULT_TEMPLATE_SHEET_URL=your_template_sheet_url_here

# HTTP Client Timeouts (in seconds)
# Increased timeouts to handle slow Lark API responses
LARKSUITE_HTTP_TIMEOUT=60
LARKSUITE_CONNECT_TIMEOUT=30
LARKSUITE_READ_TIMEOUT=60

# Retry Configuration
# Maximum number of retry attempts for failed requests
LARKSUITE_MAX_RETRY_ATTEMPTS=3

# Base delay in seconds for exponential backoff
LARKSUITE_BASE_DELAY=2

# Maximum delay in seconds (cap for exponential backoff)
LARKSUITE_MAX_DELAY=120

# Whether to use exponential backoff (true) or linear backoff (false)
LARKSUITE_EXPONENTIAL_BACKOFF=true

# Whether to add random jitter to delays to avoid thundering herd
LARKSUITE_RETRY_JITTER=true

# Sheets API Specific Settings
# These settings are specifically for sheet operations which tend to be slower
LARKSUITE_SHEETS_MAX_RETRIES=3
LARKSUITE_SHEETS_BASE_DELAY=3
LARKSUITE_SHEETS_RATE_LIMIT_DELAY=2

# Logging Configuration
LARKSUITE_LOG_CHANNEL=lark_daily
LARKSUITE_LOG_REQUESTS=true
LARKSUITE_LOG_RESPONSES=true
LARKSUITE_LOG_RETRIES=true

# Rate Limiting (optional)
LARKSUITE_REQUESTS_PER_MINUTE=100
LARKSUITE_DELAY_BETWEEN_REQUESTS=0.6

# Recommended Production Settings:
# For production environments with high load, consider these values:
# LARKSUITE_HTTP_TIMEOUT=90
# LARKSUITE_MAX_RETRY_ATTEMPTS=5
# LARKSUITE_BASE_DELAY=3
# LARKSUITE_MAX_DELAY=180
# LARKSUITE_SHEETS_MAX_RETRIES=5
# LARKSUITE_SHEETS_BASE_DELAY=5
